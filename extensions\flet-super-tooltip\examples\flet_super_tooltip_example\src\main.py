import flet as ft

from flet_super_tooltip import FletSuperTooltip, TooltipDirection, CloseButtonType


def main(page: ft.Page):
    page.title = "Flet Super Tooltip Example"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER

    # Create a tooltip reference for programmatic control
    tooltip_ref = ft.Ref[FletSuperTooltip]()

    def on_tooltip_show(e):
        print("Tooltip shown!")

    def on_tooltip_hide(e):
        print("Tooltip hidden!")

    def show_tooltip_click(e):
        tooltip_ref.current.show_tooltip()

    def hide_tooltip_click(e):
        tooltip_ref.current.hide_tooltip()

    def toggle_tooltip_click(e):
        tooltip_ref.current.toggle_tooltip()

    # Create the super tooltip with a child widget
    super_tooltip = FletSuperTooltip(
        ref=tooltip_ref,
        content="This is a super tooltip! It's much more flexible than the standard tooltip.",
        popup_direction=TooltipDirection.DOWN,
        show_close_button=True,
        close_button_type=CloseButtonType.INSIDE,
        background_color="#2196F3",
        border_color="#1976D2",
        border_width=2,
        border_radius=12,
        arrow_length=15,
        arrow_base_width=25,
        elevation=8,
        show_barrier=True,
        barrier_color="#80000000",  # Semi-transparent black
        fade_in_duration=300,
        fade_out_duration=150,
        on_show=on_tooltip_show,
        on_hide=on_tooltip_hide,
        # Child widget that triggers the tooltip
        controls=[
            ft.Container(
                width=60,
                height=60,
                bgcolor=ft.Colors.BLUE,
                border_radius=30,
                content=ft.Icon(
                    ft.Icons.INFO,
                    color=ft.Colors.WHITE,
                    size=30,
                ),
                alignment=ft.alignment.center,
            )
        ],
    )

    page.add(
        ft.Column(
            [
                ft.Text("Flet Super Tooltip Demo", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Click the blue circle to show the tooltip", size=16),
                ft.Container(height=20),
                super_tooltip,
                ft.Container(height=30),
                ft.Row(
                    [
                        ft.ElevatedButton("Show Tooltip", on_click=show_tooltip_click),
                        ft.ElevatedButton("Hide Tooltip", on_click=hide_tooltip_click),
                        ft.ElevatedButton("Toggle Tooltip", on_click=toggle_tooltip_click),
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        )
    )


ft.app(main)
