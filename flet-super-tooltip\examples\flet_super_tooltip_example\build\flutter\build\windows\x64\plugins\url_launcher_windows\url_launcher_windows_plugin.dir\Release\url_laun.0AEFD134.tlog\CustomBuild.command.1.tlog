^C:\USERS\<USER>\DESKTOP\FLET\APP_VIEW\APP_VIEW\FLET-SUPER-TOOLTIP\EXAMPLES\FLET_SUPER_TOOLTIP_EXAMPLE\BUILD\FLUTTER\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/flet/app_view/app_view/flet-super-tooltip/examples/flet_super_tooltip_example/build/flutter/windows -BC:/Users/<USER>/Desktop/flet/app_view/app_view/flet-super-tooltip/examples/flet_super_tooltip_example/build/flutter/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/flet/app_view/app_view/flet-super-tooltip/examples/flet_super_tooltip_example/build/flutter/build/windows/x64/plugins/url_launcher_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
