
import 'package:flet/flet.dart';
import 'package:flutter/material.dart';
import 'package:super_tooltip/super_tooltip.dart';

class FletSuperTooltipControl extends StatelessWidget {
  final Control? parent;
  final Control control;
  final FletControlBackend? backend;

  const FletSuperTooltipControl({
    super.key,
    required this.parent,
    required this.control,
    this.backend,
  });

  @override
  Widget build(BuildContext context) {
    final tooltip = SuperTooltip(
      popupDirection: TooltipDirection.down,
      showCloseButton: control.attrBool("show_close_button", true),
      closeButtonColor: _parseColor(control.attrString("close_button_color")),
      backgroundColor: _parseColor(control.attrString("background_color")),
      borderColor: _parseColor(control.attrString("border_color")),
      borderWidth: control.attrDouble("border_width", 2.0) ?? 2.0,
      borderRadius: control.attrDouble("border_radius", 8.0) ?? 8.0,
      arrowTipDistance: control.attrDouble("arrow_tip_distance", 8.0) ?? 8.0,
      arrowBaseWidth: control.attrDouble("arrow_base_width", 20.0) ?? 20.0,
      arrowLength: control.attrDouble("arrow_length", 10.0) ?? 10.0,
      shadowColor: _parseColor(control.attrString("shadow_color")),
      elevation: control.attrDouble("elevation", 4.0) ?? 4.0,
      content: Text(control.attrString("value", "") ?? ""),
      onClose: () {
        if (backend != null) {
          backend!.trigger("close", {});
        }
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      tooltip.show(context);
    });

    return constrainedControl(context, Container(), parent, control);
  }

  Color? _parseColor(String? colorStr) {
    if (colorStr == null) return null;
    try {
      return Color(int.parse(colorStr.replaceFirst('#', '0xff')));
    } catch (_) {
      return null;
    }
  }
}
