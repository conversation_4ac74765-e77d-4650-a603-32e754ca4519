from flet_package_guide import ConstrainedControl

class SuperTooltip(ConstrainedControl):
    def __init__(self,
                 content=None,
                 showCloseButton=True,
                 closeButtonColor=None,
                 backgroundColor=None,
                 borderColor=None,
                 borderWidth=2.0,
                 borderRadius=8.0,
                 arrowTipDistance=8.0,
                 arrowBaseWidth=20.0,
                 arrowLength=10.0,
                 shadowColor=None,
                 elevation=4.0,
                 on_close=None):
        super().__init__()
        self.content = content
        self.showCloseButton = showCloseButton
        self.closeButtonColor = closeButtonColor
        self.backgroundColor = backgroundColor
        self.borderColor = borderColor
        self.borderWidth = borderWidth
        self.borderRadius = borderRadius
        self.arrowTipDistance = arrowTipDistance
        self.arrowBaseWidth = arrowBaseWidth
        self.arrowLength = arrowLength
        self.shadowColor = shadowColor
        self.elevation = elevation
        self.on_close = on_close

    @property
    def content(self):
        return self._get_attr("content")

    @content.setter
    def content(self, value):
        self._set_attr("content", value)

    @property
    def showCloseButton(self):
        return self._get_attr("showCloseButton", data_type="bool", def_value=True)

    @showCloseButton.setter
    def showCloseButton(self, value):
        self._set_attr("showCloseButton", value)

    @property
    def closeButtonColor(self):
        return self._get_attr("closeButtonColor")

    @closeButtonColor.setter
    def closeButtonColor(self, value):
        self._set_attr("closeButtonColor", value)

    @property
    def backgroundColor(self):
        return self._get_attr("backgroundColor")

    @backgroundColor.setter
    def backgroundColor(self, value):
        self._set_attr("backgroundColor", value)

    @property
    def borderColor(self):
        return self._get_attr("borderColor")

    @borderColor.setter
    def borderColor(self, value):
        self._set_attr("borderColor", value)

    @property
    def borderWidth(self):
        return self._get_attr("borderWidth", data_type="float", def_value=2.0)

    @borderWidth.setter
    def borderWidth(self, value):
        self._set_attr("borderWidth", value)

    @property
    def borderRadius(self):
        return self._get_attr("borderRadius", data_type="float", def_value=8.0)

    @borderRadius.setter
    def borderRadius(self, value):
        self._set_attr("borderRadius", value)

    @property
    def arrowTipDistance(self):
        return self._get_attr("arrowTipDistance", data_type="float", def_value=8.0)

    @arrowTipDistance.setter
    def arrowTipDistance(self, value):
        self._set_attr("arrowTipDistance", value)

    @property
    def arrowBaseWidth(self):
        return self._get_attr("arrowBaseWidth", data_type="float", def_value=20.0)

    @arrowBaseWidth.setter
    def arrowBaseWidth(self, value):
        self._set_attr("arrowBaseWidth", value)

    @property
    def arrowLength(self):
        return self._get_attr("arrowLength", data_type="float", def_value=10.0)

    @arrowLength.setter
    def arrowLength(self, value):
        self._set_attr("arrowLength", value)

    @property
    def shadowColor(self):
        return self._get_attr("shadowColor")

    @shadowColor.setter
    def shadowColor(self, value):
        self._set_attr("shadowColor", value)

    @property
    def elevation(self):
        return self._get_attr("elevation", data_type="float", def_value=4.0)

    @elevation.setter
    def elevation(self, value):
        self._set_attr("elevation", value)

    @property
    def on_close(self):
        return self._get_event_handler("close")

    @on_close.setter
    def on_close(self, handler):
        self._set_event_handler("close", handler)

    def _get_control_name(self):
        return "super_tooltip"
