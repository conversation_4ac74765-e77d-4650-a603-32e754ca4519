[project]
name = "flet-super-tooltip"
version = "0.1.0"
description = "FletSuperTooltip control for Flet"
readme = "README.md"
requires-python = ">=3.9"
authors = [
    { name = "Flet contributors", email = "<EMAIL>" }
]
dependencies = [
    "flet>=0.28.3",
]

[project.urls]
Homepage = "https://mydomain.dev"
Documentation = "https://github.com/MyGithubAccount/flet-super-tooltip"
Repository = "https://github.com/MyGithubAccount/flet-super-tooltip"
Issues = "https://github.com/MyGithubAccount/flet-super-tooltip/issues"

[tool.setuptools.package-data]
"flutter.flet_super_tooltip" = ["**/*"]

[tool.uv]
dev-dependencies = [
    "flet[all]==0.28.3",
    "mkdocs", 
    "mkdocs-material",
    "mkdocstrings[python]"
]

[tool.poetry.group.dev.dependencies]
flet = {extras = ["all"], version = "0.28.3"}
mkdocs = "*"
mkdocstrings = { extras = ["python"], version = "*" }
mkdocs-material = "*"

[tool.setuptools]
license-files = []

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"
