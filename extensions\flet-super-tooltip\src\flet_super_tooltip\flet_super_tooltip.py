from enum import Enum
from typing import Any, Optional, Union

from flet.core.constrained_control import ConstrainedControl
from flet.core.control import OptionalNumber
from flet.core.ref import Ref


class TooltipDirection(Enum):
    UP = "up"
    DOWN = "down"
    LEFT = "left"
    RIGHT = "right"


class CloseButtonType(Enum):
    INSIDE = "inside"
    OUTSIDE = "outside"


class FletSuperTooltip(ConstrainedControl):
    """
    A powerful and customizable tooltip widget for Flet.

    FletSuperTooltip provides a flexible and feature-rich way to display tooltips
    in your Flet applications. It wraps the Flutter super_tooltip package.
    """

    def __init__(
        self,
        content: Optional[str] = None,
        popup_direction: Optional[TooltipDirection] = TooltipDirection.DOWN,
        show_close_button: Optional[bool] = False,
        close_button_type: Optional[CloseButtonType] = CloseButtonType.INSIDE,
        close_button_color: Optional[str] = None,
        close_button_size: OptionalNumber = None,
        show_barrier: Optional[bool] = None,
        barrier_color: Optional[str] = None,
        background_color: Optional[str] = None,
        border_color: Optional[str] = None,
        border_width: OptionalNumber = 0.0,
        border_radius: OptionalNumber = 10.0,
        arrow_tip_distance: OptionalNumber = 2.0,
        arrow_base_width: OptionalNumber = 20.0,
        arrow_length: OptionalNumber = 20.0,
        arrow_tip_radius: OptionalNumber = 0.0,
        shadow_color: Optional[str] = None,
        elevation: OptionalNumber = 0.0,
        has_shadow: Optional[bool] = None,
        shadow_blur_radius: OptionalNumber = None,
        shadow_spread_radius: OptionalNumber = None,
        minimum_outside_margin: OptionalNumber = 20.0,
        vertical_offset: OptionalNumber = 0.0,
        fade_in_duration: OptionalNumber = 150,
        fade_out_duration: OptionalNumber = 0,
        show_drop_box_filter: Optional[bool] = False,
        sigma_x: OptionalNumber = 5.0,
        sigma_y: OptionalNumber = 5.0,
        hide_tooltip_on_tap: Optional[bool] = False,
        hide_tooltip_on_barrier_tap: Optional[bool] = True,
        toggle_on_tap: Optional[bool] = False,
        show_on_tap: Optional[bool] = True,
        click_through: Optional[bool] = False,
        on_show=None,
        on_hide=None,
        on_long_press=None,
        #
        # ConstrainedControl
        #
        ref: Optional[Ref] = None,
        key: Optional[str] = None,
        width: OptionalNumber = None,
        height: OptionalNumber = None,
        left: OptionalNumber = None,
        top: OptionalNumber = None,
        right: OptionalNumber = None,
        bottom: OptionalNumber = None,
        expand: Union[None, bool, int] = None,
        expand_loose: Optional[bool] = None,
        col: Optional[Union[int, dict]] = None,
        opacity: OptionalNumber = None,
        rotate: OptionalNumber = None,
        scale: OptionalNumber = None,
        offset: Optional[Union[tuple, list]] = None,
        aspect_ratio: OptionalNumber = None,
        animate_opacity: Optional[Union[bool, int]] = None,
        animate_size: Optional[Union[bool, int]] = None,
        animate_position: Optional[Union[bool, int]] = None,
        animate_rotation: Optional[Union[bool, int]] = None,
        animate_scale: Optional[Union[bool, int]] = None,
        animate_offset: Optional[Union[bool, int]] = None,
        on_animation_end=None,
        tooltip: Optional[str] = None,
        visible: Optional[bool] = None,
        disabled: Optional[bool] = None,
        data: Any = None,
        **kwargs
    ):
        ConstrainedControl.__init__(
            self,
            ref=ref,
            key=key,
            width=width,
            height=height,
            left=left,
            top=top,
            right=right,
            bottom=bottom,
            expand=expand,
            expand_loose=expand_loose,
            col=col,
            opacity=opacity,
            rotate=rotate,
            scale=scale,
            offset=offset,
            aspect_ratio=aspect_ratio,
            animate_opacity=animate_opacity,
            animate_size=animate_size,
            animate_position=animate_position,
            animate_rotation=animate_rotation,
            animate_scale=animate_scale,
            animate_offset=animate_offset,
            on_animation_end=on_animation_end,
            tooltip=tooltip,
            visible=visible,
            disabled=disabled,
            data=data,
            **kwargs
        )

        self.content = content
        self.popup_direction = popup_direction
        self.show_close_button = show_close_button
        self.close_button_type = close_button_type
        self.close_button_color = close_button_color
        self.close_button_size = close_button_size
        self.show_barrier = show_barrier
        self.barrier_color = barrier_color
        self.background_color = background_color
        self.border_color = border_color
        self.border_width = border_width
        self.border_radius = border_radius
        self.arrow_tip_distance = arrow_tip_distance
        self.arrow_base_width = arrow_base_width
        self.arrow_length = arrow_length
        self.arrow_tip_radius = arrow_tip_radius
        self.shadow_color = shadow_color
        self.elevation = elevation
        self.has_shadow = has_shadow
        self.shadow_blur_radius = shadow_blur_radius
        self.shadow_spread_radius = shadow_spread_radius
        self.minimum_outside_margin = minimum_outside_margin
        self.vertical_offset = vertical_offset
        self.fade_in_duration = fade_in_duration
        self.fade_out_duration = fade_out_duration
        self.show_drop_box_filter = show_drop_box_filter
        self.sigma_x = sigma_x
        self.sigma_y = sigma_y
        self.hide_tooltip_on_tap = hide_tooltip_on_tap
        self.hide_tooltip_on_barrier_tap = hide_tooltip_on_barrier_tap
        self.toggle_on_tap = toggle_on_tap
        self.show_on_tap = show_on_tap
        self.click_through = click_through
        self.on_show = on_show
        self.on_hide = on_hide
        self.on_long_press = on_long_press

    def _get_control_name(self):
        return "flet_super_tooltip"

    # Methods to control tooltip visibility
    def show_tooltip(self):
        """Show the tooltip programmatically."""
        self.invoke_method("show_tooltip")

    def hide_tooltip(self):
        """Hide the tooltip programmatically."""
        self.invoke_method("hide_tooltip")

    def toggle_tooltip(self):
        """Toggle the tooltip visibility."""
        self.invoke_method("toggle_tooltip")

    # Properties
    @property
    def content(self):
        return self._get_attr("content")

    @content.setter
    def content(self, v):
        self._set_attr("content", v)

    @property
    def popup_direction(self):
        return self._get_attr("popup_direction")

    @popup_direction.setter
    def popup_direction(self, v):
        self._set_attr("popup_direction", v.value if isinstance(v, TooltipDirection) else v)

    @property
    def show_close_button(self):
        return self._get_attr("show_close_button", data_type="bool", def_value=False)

    @show_close_button.setter
    def show_close_button(self, v):
        self._set_attr("show_close_button", v)

    @property
    def close_button_type(self):
        return self._get_attr("close_button_type")

    @close_button_type.setter
    def close_button_type(self, v):
        self._set_attr("close_button_type", v.value if isinstance(v, CloseButtonType) else v)

    @property
    def close_button_color(self):
        return self._get_attr("close_button_color")

    @close_button_color.setter
    def close_button_color(self, v):
        self._set_attr("close_button_color", v)

    @property
    def close_button_size(self):
        return self._get_attr("close_button_size", data_type="float")

    @close_button_size.setter
    def close_button_size(self, v):
        self._set_attr("close_button_size", v)

    @property
    def show_barrier(self):
        return self._get_attr("show_barrier", data_type="bool")

    @show_barrier.setter
    def show_barrier(self, v):
        self._set_attr("show_barrier", v)

    @property
    def barrier_color(self):
        return self._get_attr("barrier_color")

    @barrier_color.setter
    def barrier_color(self, v):
        self._set_attr("barrier_color", v)

    @property
    def background_color(self):
        return self._get_attr("background_color")

    @background_color.setter
    def background_color(self, v):
        self._set_attr("background_color", v)

    @property
    def border_color(self):
        return self._get_attr("border_color")

    @border_color.setter
    def border_color(self, v):
        self._set_attr("border_color", v)

    @property
    def border_width(self):
        return self._get_attr("border_width", data_type="float", def_value=0.0)

    @border_width.setter
    def border_width(self, v):
        self._set_attr("border_width", v)

    @property
    def border_radius(self):
        return self._get_attr("border_radius", data_type="float", def_value=10.0)

    @border_radius.setter
    def border_radius(self, v):
        self._set_attr("border_radius", v)

    @property
    def arrow_tip_distance(self):
        return self._get_attr("arrow_tip_distance", data_type="float", def_value=2.0)

    @arrow_tip_distance.setter
    def arrow_tip_distance(self, v):
        self._set_attr("arrow_tip_distance", v)

    @property
    def arrow_base_width(self):
        return self._get_attr("arrow_base_width", data_type="float", def_value=20.0)

    @arrow_base_width.setter
    def arrow_base_width(self, v):
        self._set_attr("arrow_base_width", v)

    @property
    def arrow_length(self):
        return self._get_attr("arrow_length", data_type="float", def_value=20.0)

    @arrow_length.setter
    def arrow_length(self, v):
        self._set_attr("arrow_length", v)

    @property
    def arrow_tip_radius(self):
        return self._get_attr("arrow_tip_radius", data_type="float", def_value=0.0)

    @arrow_tip_radius.setter
    def arrow_tip_radius(self, v):
        self._set_attr("arrow_tip_radius", v)

    @property
    def shadow_color(self):
        return self._get_attr("shadow_color")

    @shadow_color.setter
    def shadow_color(self, v):
        self._set_attr("shadow_color", v)

    @property
    def elevation(self):
        return self._get_attr("elevation", data_type="float", def_value=0.0)

    @elevation.setter
    def elevation(self, v):
        self._set_attr("elevation", v)

    @property
    def has_shadow(self):
        return self._get_attr("has_shadow", data_type="bool")

    @has_shadow.setter
    def has_shadow(self, v):
        self._set_attr("has_shadow", v)

    @property
    def shadow_blur_radius(self):
        return self._get_attr("shadow_blur_radius", data_type="float")

    @shadow_blur_radius.setter
    def shadow_blur_radius(self, v):
        self._set_attr("shadow_blur_radius", v)

    @property
    def shadow_spread_radius(self):
        return self._get_attr("shadow_spread_radius", data_type="float")

    @shadow_spread_radius.setter
    def shadow_spread_radius(self, v):
        self._set_attr("shadow_spread_radius", v)

    @property
    def minimum_outside_margin(self):
        return self._get_attr("minimum_outside_margin", data_type="float", def_value=20.0)

    @minimum_outside_margin.setter
    def minimum_outside_margin(self, v):
        self._set_attr("minimum_outside_margin", v)

    @property
    def vertical_offset(self):
        return self._get_attr("vertical_offset", data_type="float", def_value=0.0)

    @vertical_offset.setter
    def vertical_offset(self, v):
        self._set_attr("vertical_offset", v)

    @property
    def fade_in_duration(self):
        return self._get_attr("fade_in_duration", data_type="float", def_value=150)

    @fade_in_duration.setter
    def fade_in_duration(self, v):
        self._set_attr("fade_in_duration", v)

    @property
    def fade_out_duration(self):
        return self._get_attr("fade_out_duration", data_type="float", def_value=0)

    @fade_out_duration.setter
    def fade_out_duration(self, v):
        self._set_attr("fade_out_duration", v)

    @property
    def show_drop_box_filter(self):
        return self._get_attr("show_drop_box_filter", data_type="bool", def_value=False)

    @show_drop_box_filter.setter
    def show_drop_box_filter(self, v):
        self._set_attr("show_drop_box_filter", v)

    @property
    def sigma_x(self):
        return self._get_attr("sigma_x", data_type="float", def_value=5.0)

    @sigma_x.setter
    def sigma_x(self, v):
        self._set_attr("sigma_x", v)

    @property
    def sigma_y(self):
        return self._get_attr("sigma_y", data_type="float", def_value=5.0)

    @sigma_y.setter
    def sigma_y(self, v):
        self._set_attr("sigma_y", v)

    @property
    def hide_tooltip_on_tap(self):
        return self._get_attr("hide_tooltip_on_tap", data_type="bool", def_value=False)

    @hide_tooltip_on_tap.setter
    def hide_tooltip_on_tap(self, v):
        self._set_attr("hide_tooltip_on_tap", v)

    @property
    def hide_tooltip_on_barrier_tap(self):
        return self._get_attr("hide_tooltip_on_barrier_tap", data_type="bool", def_value=True)

    @hide_tooltip_on_barrier_tap.setter
    def hide_tooltip_on_barrier_tap(self, v):
        self._set_attr("hide_tooltip_on_barrier_tap", v)

    @property
    def toggle_on_tap(self):
        return self._get_attr("toggle_on_tap", data_type="bool", def_value=False)

    @toggle_on_tap.setter
    def toggle_on_tap(self, v):
        self._set_attr("toggle_on_tap", v)

    @property
    def show_on_tap(self):
        return self._get_attr("show_on_tap", data_type="bool", def_value=True)

    @show_on_tap.setter
    def show_on_tap(self, v):
        self._set_attr("show_on_tap", v)

    @property
    def click_through(self):
        return self._get_attr("click_through", data_type="bool", def_value=False)

    @click_through.setter
    def click_through(self, v):
        self._set_attr("click_through", v)

    # Event handlers
    @property
    def on_show(self):
        return self._get_event_handler("show")

    @on_show.setter
    def on_show(self, handler):
        self._set_event_handler("show", handler)

    @property
    def on_hide(self):
        return self._get_event_handler("hide")

    @on_hide.setter
    def on_hide(self, handler):
        self._set_event_handler("hide", handler)

    @property
    def on_long_press(self):
        return self._get_event_handler("long_press")

    @on_long_press.setter
    def on_long_press(self, handler):
        self._set_event_handler("long_press", handler)
