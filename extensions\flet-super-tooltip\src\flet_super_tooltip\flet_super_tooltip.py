from enum import Enum
from typing import Any, Optional

from flet.core.constrained_control import ConstrainedControl
from flet.core.control import OptionalNumber

class FletSuperTooltip(ConstrainedControl):
    """
    FletSuperTooltip Control description.
    """


    def __init__(
        self,
        opacity: OptionalNumber = None,
        tooltip: Optional[str] = None,
        visible: Optional[bool] = None,
        data: Any = None,
        left: OptionalNumber = None,
        top: OptionalNumber = None,
        right: OptionalNumber = None,
        bottom: OptionalNumber = None,
        value: Optional[str] = None,
        show_close_button: Optional[bool] = True,
        close_button_color: Optional[str] = None,
        background_color: Optional[str] = None,
        border_color: Optional[str] = None,
        border_width: OptionalNumber = 2.0,
        border_radius: OptionalNumber = 8.0,
        arrow_tip_distance: OptionalNumber = 8.0,
        arrow_base_width: OptionalNumber = 20.0,
        arrow_length: OptionalNumber = 10.0,
        shadow_color: Optional[str] = None,
        elevation: OptionalNumber = 4.0,
        on_close=None,
        **kwargs
    ):
        ConstrainedControl.__init__(
            self,
            tooltip=tooltip,
            opacity=opacity,
            visible=visible,
            data=data,
            left=left,
            top=top,
            right=right,
            bottom=bottom,
            **kwargs
        )
        self.value = value
        self.show_close_button = show_close_button
        self.close_button_color = close_button_color
        self.background_color = background_color
        self.border_color = border_color
        self.border_width = border_width
        self.border_radius = border_radius
        self.arrow_tip_distance = arrow_tip_distance
        self.arrow_base_width = arrow_base_width
        self.arrow_length = arrow_length
        self.shadow_color = shadow_color
        self.elevation = elevation
        self.on_close = on_close

    def _get_control_name(self):
        return "flet_super_tooltip"

    @property
    def value(self):
        return self._get_attr("value")

    @value.setter
    def value(self, v):
        self._set_attr("value", v)

    @property
    def show_close_button(self):
        return self._get_attr("show_close_button", data_type="bool", def_value=True)

    @show_close_button.setter
    def show_close_button(self, v):
        self._set_attr("show_close_button", v)

    @property
    def close_button_color(self):
        return self._get_attr("close_button_color")

    @close_button_color.setter
    def close_button_color(self, v):
        self._set_attr("close_button_color", v)

    @property
    def background_color(self):
        return self._get_attr("background_color")

    @background_color.setter
    def background_color(self, v):
        self._set_attr("background_color", v)

    @property
    def border_color(self):
        return self._get_attr("border_color")

    @border_color.setter
    def border_color(self, v):
        self._set_attr("border_color", v)

    @property
    def border_width(self):
        return self._get_attr("border_width", data_type="float", def_value=2.0)

    @border_width.setter
    def border_width(self, v):
        self._set_attr("border_width", v)

    @property
    def border_radius(self):
        return self._get_attr("border_radius", data_type="float", def_value=8.0)

    @border_radius.setter
    def border_radius(self, v):
        self._set_attr("border_radius", v)

    @property
    def arrow_tip_distance(self):
        return self._get_attr("arrow_tip_distance", data_type="float", def_value=8.0)

    @arrow_tip_distance.setter
    def arrow_tip_distance(self, v):
        self._set_attr("arrow_tip_distance", v)

    @property
    def arrow_base_width(self):
        return self._get_attr("arrow_base_width", data_type="float", def_value=20.0)

    @arrow_base_width.setter
    def arrow_base_width(self, v):
        self._set_attr("arrow_base_width", v)

    @property
    def arrow_length(self):
        return self._get_attr("arrow_length", data_type="float", def_value=10.0)

    @arrow_length.setter
    def arrow_length(self, v):
        self._set_attr("arrow_length", v)

    @property
    def shadow_color(self):
        return self._get_attr("shadow_color")

    @shadow_color.setter
    def shadow_color(self, v):
        self._set_attr("shadow_color", v)

    @property
    def elevation(self):
        return self._get_attr("elevation", data_type="float", def_value=4.0)

    @elevation.setter
    def elevation(self, v):
        self._set_attr("elevation", v)

    @property
    def on_close(self):
        return self._get_event_handler("close")

    @on_close.setter
    def on_close(self, handler):
        self._set_event_handler("close", handler)
